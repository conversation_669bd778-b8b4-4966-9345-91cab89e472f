"""
Simple Generic Product Extraction Example

This demonstrates basic structured data extraction using the SOME library.
No evaluation - just pure extraction functionality.
"""
from __future__ import annotations

import argparse
from pathlib import Path
from typing import Dict, Any, List

from some.inference import get_language_model
from some.metrics import LLMMetricsCollector
from some.io import read_json
from .my_prompt import ProductPrompt


def load_dataset(dataset_path: str = "dataset.json") -> List[Dict[str, Any]]:
    """Load the dataset from JSON file."""
    current_dir = Path(__file__).parent
    full_path = current_dir / dataset_path
    
    if not full_path.exists():
        raise FileNotFoundError(f"Dataset file not found: {full_path}")
    
    return read_json(str(full_path))


def main(dataset_path: str = "dataset.json"):
    """Main function for running the extraction example."""
    print("🚀 Generic Product Extraction")
    print("=" * 40)
    
    # Load dataset from JSON file
    try:
        dataset = load_dataset(dataset_path)
        print(f"📋 Loaded {len(dataset)} items from {dataset_path}")
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure the dataset.json file exists in the same directory")
        return None
    
    # Extract text items for processing
    items = [{"text": item["text"]} for item in dataset]
    
    # Build inputs using the prompt builder
    inputs = [ProductPrompt().build(x) for x in items]
    
    # Get language model
    provider = "openai"
    model = "gpt-4o-mini"
    
    print(f"Using {provider} language model")
    lm = get_language_model(provider=provider, model=model)
    
    # Setup metrics collection
    llm_collector = LLMMetricsCollector(
        name="Product_Extraction",
        cost_per_input_token=0.15/1000000,   # GPT-4o-mini pricing
        cost_per_output_token=0.6/1000000
    )
    
    # Run extraction with automatic metrics collection
    results, effective_workers, extraction_time = lm.generate(inputs, metrics_collector=llm_collector)
    
    print(f"Extraction completed using {effective_workers} workers in {extraction_time:.4f}s")
    
    # Display extraction results
    print(f"\n📊 Extraction Results ({len(results)} items):")
    successful_extractions = 0
    for i, r in enumerate(results):
        product = r.get('product')
        if product:
            successful_extractions += 1
            print(f"  Item {i+1} ({dataset[i]['id']}): {product}")
        else:
            print(f"  Item {i+1} ({dataset[i]['id']}): ❌ {r.get('error', 'No result')}")
    
    print(f"\n✅ Successful extractions: {successful_extractions}/{len(results)}")
    
    # Collect LLM performance metrics
    llm_metrics = llm_collector.collect_metrics(results)
    
    print("\n" + "=" * 50)
    print("📈 LLM PERFORMANCE METRICS")
    print("=" * 50)
    print(llm_collector.format_summary(llm_metrics))

    print("\n" + "=" * 50)
    print("💡 SUMMARY")
    print("=" * 50)
    print(f"Dataset items: {len(dataset)}")
    print(f"Successful extractions: {successful_extractions}/{len(results)} ({successful_extractions/len(results)*100:.1f}%)")
    print(f"Total cost: ${llm_metrics['total_cost']:.6f}")
    print(f"Total time: {llm_metrics['total_inference_time']:.2f}s")

    print("\n🎉 Extraction completed successfully!")
    
    return {
        "dataset": dataset,
        "extraction_results": results,
        "llm_metrics": llm_metrics,
        "successful_extractions": successful_extractions
    }


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run simple product extraction"
    )
    parser.add_argument(
        "--dataset",
        type=str,
        default="dataset.json",
        help="Path to the JSON dataset file (default: dataset.json)"
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    
    print(f"🔧 Configuration:")
    print(f"  Dataset: {args.dataset}")
    print()
    
    # Run extraction
    results = main(dataset_path=args.dataset)
    
    if results:
        print(f"\n🎯 Final Summary:")
        print(f"  Processed {len(results['dataset'])} items from dataset")
        print(f"  Extraction success rate: {results['successful_extractions']}/{len(results['dataset'])}")
        print(f"  Total cost: ${results['llm_metrics']['total_cost']:.6f}")
        print(f"  Total time: {results['llm_metrics']['total_inference_time']:.2f}s")
    else:
        print("❌ Execution failed. Please check the error messages above.")
